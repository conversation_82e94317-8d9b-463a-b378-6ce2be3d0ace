/*
Copyright (C) 2025 QuantumNous

This program is free software: you can redistribute it and/or modify
it under the terms of the GNU Affero General Public License as
published by the Free Software Foundation, either version 3 of the
License, or (at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
GNU Affero General Public License for more details.

You should have received a copy of the GNU Affero General Public License
along with this program. If not, see <https://www.gnu.org/licenses/>.

For commercial licensing, <NAME_EMAIL>
*/

import React, { useState } from 'react';
import {Button, Form, Toast} from '@douyinfe/semi-ui';
import {IconSearch, IconDownload} from '@douyinfe/semi-icons';

const LogsFilters = ({
                         formInitValues,
                         setFormApi,
                         refresh,
                         setShowColumnSelector,
                         formApi,
                         setLogType,
                         loading,
                         isAdminUser,
                         t,
                     }) => {
    const [exportLoading, setExportLoading] = useState(false);

    // 导出Excel功能
    const handleExport = async () => {
        if (!formApi) {
            Toast.error(t('表单未初始化'));
            return;
        }

        setExportLoading(true);
        try {
            const formValues = formApi.getValues();
            const params = new URLSearchParams();

            // 处理日期范围
            if (formValues.dateRange && formValues.dateRange.length === 2) {
                const startTimestamp = Math.floor(formValues.dateRange[0].getTime() / 1000);
                const endTimestamp = Math.floor(formValues.dateRange[1].getTime() / 1000);
                params.append('start_timestamp', startTimestamp.toString());
                params.append('end_timestamp', endTimestamp.toString());
            }

            // 处理其他筛选条件
            if (formValues.logType && formValues.logType !== '0') {
                params.append('type', formValues.logType);
            }
            if (formValues.token_name) {
                params.append('token_name', formValues.token_name);
            }
            if (formValues.model_name) {
                params.append('model_name', formValues.model_name);
            }
            if (formValues.group) {
                params.append('group', formValues.group);
            }
            if (isAdminUser) {
                if (formValues.channel) {
                    params.append('channel', formValues.channel);
                }
                if (formValues.username) {
                    params.append('username', formValues.username);
                }
            }

            // 构建导出URL
            const exportUrl = `/api/log/export?${params.toString()}`;

            // 使用fetch进行下载，这样可以正确处理session认证
            const response = await fetch(exportUrl, {
                method: 'GET',
                credentials: 'include', // 包含cookies/session
                headers: {
                    'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                },
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // 获取文件名
            const contentDisposition = response.headers.get('Content-Disposition');
            let filename = 'logs_export.xlsx';
            if (contentDisposition) {
                const filenameMatch = contentDisposition.match(/filename=(.+)/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // 创建blob并下载
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            Toast.success(t('Excel文件导出成功'));
        } catch (error) {
            console.error('导出失败:', error);
            if (error.message.includes('401')) {
                Toast.error(t('认证失败，请重新登录'));
            } else if (error.message.includes('403')) {
                Toast.error(t('权限不足，需要管理员权限'));
            } else {
                Toast.error(t('导出失败，请重试'));
            }
        } finally {
            setExportLoading(false);
        }
    };
    return (
        <Form
            initValues={formInitValues}
            getFormApi={(api) => setFormApi(api)}
            onSubmit={refresh}
            allowEmpty={true}
            autoComplete='off'
            layout='vertical'
            trigger='change'
            stopValidateWithError={false}
        >
            <div className='flex flex-col gap-2'>
                <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2'>
                    {/* 时间选择器 */}
                    <div className='col-span-1 lg:col-span-2'>
                        <Form.DatePicker
                            field='dateRange'
                            className='w-full'
                            type='dateTimeRange'
                            placeholder={[t('开始时间'), t('结束时间')]}
                            showClear
                            pure
                            size='small'
                        />
                    </div>

                    {/* 其他搜索字段 */}
                    <Form.Input
                        field='token_name'
                        prefix={<IconSearch/>}
                        placeholder={t('令牌名称')}
                        showClear
                        pure
                        size='small'
                    />

                    <Form.Input
                        field='model_name'
                        prefix={<IconSearch/>}
                        placeholder={t('模型名称')}
                        showClear
                        pure
                        size='small'
                    />

                    <Form.Input
                        field='group'
                        prefix={<IconSearch/>}
                        placeholder={t('分组')}
                        showClear
                        pure
                        size='small'
                    />

                    {isAdminUser && (
                        <>
                            <Form.Input
                                field='channel'
                                prefix={<IconSearch/>}
                                placeholder={t('渠道 ID')}
                                showClear
                                pure
                                size='small'
                            />
                            <Form.Input
                                field='username'
                                prefix={<IconSearch/>}
                                placeholder={t('用户名称')}
                                showClear
                                pure
                                size='small'
                            />
                        </>
                    )}
                </div>

                {/* 操作按钮区域 */}
                <div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3'>
                    {/* 日志类型选择器 */}
                    <div className='w-full sm:w-auto'>
                        <Form.Select
                            field='logType'
                            placeholder={t('日志类型')}
                            className='w-full sm:w-auto min-w-[120px]'
                            showClear
                            pure
                            onChange={() => {
                                // 延迟执行搜索，让表单值先更新
                                setTimeout(() => {
                                    refresh();
                                }, 0);
                            }}
                            size='small'
                        >
                            <Form.Select.Option value='0'>{t('全部')}</Form.Select.Option>
                            <Form.Select.Option value='1'>{t('充值')}</Form.Select.Option>
                            <Form.Select.Option value='2'>{t('消费')}</Form.Select.Option>
                            <Form.Select.Option value='3'>{t('管理')}</Form.Select.Option>
                            <Form.Select.Option value='4'>{t('系统')}</Form.Select.Option>
                            <Form.Select.Option value='5'>{t('错误')}</Form.Select.Option>
                        </Form.Select>
                    </div>

                    <div className='flex gap-2 w-full sm:w-auto justify-end'>
                        <Button
                            type='tertiary'
                            icon={<IconDownload />}
                            onClick={handleExport}
                            loading={exportLoading}
                            size='small'
                        >
                            {t('导出Excel')}
                        </Button>
                        <Button
                            type='tertiary'
                            htmlType='submit'
                            loading={loading}
                            size='small'
                        >
                            {t('查询')}
                        </Button>
                        <Button
                            type='tertiary'
                            onClick={() => {
                                if (formApi) {
                                    formApi.reset();
                                    setLogType(0);
                                    setTimeout(() => {
                                        refresh();
                                    }, 100);
                                }
                            }}
                            size='small'
                        >
                            {t('重置')}
                        </Button>
                        <Button
                            type='tertiary'
                            onClick={() => setShowColumnSelector(true)}
                            size='small'
                        >
                            {t('列设置')}
                        </Button>
                    </div>
                </div>
            </div>
        </Form>
    );
};

export default LogsFilters;
